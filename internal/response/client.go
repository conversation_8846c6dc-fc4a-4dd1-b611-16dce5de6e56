package response

import "yotracker/internal/models"

type ClientResponse struct {
	Id                  uint            `json:"id"`
	CreatedById         uint            `json:"created_by_id"`
	ReferredById        *uint           `json:"referred_by_id"`
	BranchId            *uint           `json:"branch_id"`
	StaffId             *uint           `json:"staff_id"`
	CountryId           *uint           `json:"country_id"`
	CurrencyId          *uint           `json:"currency_id"`
	ClientType          string          `json:"client_type"`
	Gender              *string         `json:"gender"`
	Name                string          `json:"name"`
	Email               string          `json:"email"`
	Company             *string         `json:"company"`
	State               *string         `json:"state"`
	City                *string         `json:"city"`
	Town                *string         `json:"town"`
	Address             *string         `json:"address"`
	PhoneNumber         string          `json:"phone_number"`
	Status              string          `json:"status"`
	MapsProvider        *string         `json:"maps_provider"`
	BillingCycle        *string         `json:"billing_cycle"`
	BillingDay          *uint           `json:"billing_day"`
	IsLifetime          *bool           `json:"is_lifetime"`
	NextBillingDate     string          `json:"next_billing_date"`
	LastBilledAt        string          `json:"last_billed_at"`
	SuspendedAt         string          `json:"suspended_at"`
	Description         *string         `json:"description"`
	CreatedAt           string          `json:"created_at"`
	UpdatedAt           string          `json:"updated_at"`
	Country             models.Country  `json:"country"`
	Currency            models.Currency `json:"currency"`
	TotalInvoicesAmount *float64        `json:"total_invoices_amount"`
	TotalPayments       *float64        `json:"total_payments"`
	TotalBalance        *float64        `json:"total_balance"`
}
