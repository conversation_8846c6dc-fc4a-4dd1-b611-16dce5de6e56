package models

import (
	"time"
	"yotracker/config"
)

type Setting struct {
	Id           uint      `json:"id" gorm:"primaryKey"`
	Name         string    `json:"name"`
	SettingKey   string    `json:"setting_key" gorm:"unique"`
	SettingValue string    `json:"setting_value"`
	Category     *string   `json:"category" gorm:"type:varchar(255);default:general"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// AllowedSettings defines which settings can be retrieved by frontend users
var AllowedSettings = []string{
	"company_name",
	"company_address",
	"company_phone",
	"company_email",
	"google_maps_api_key",
	"invoice_reference_prefix",
	"invoice_reference_format",
	"generate_invoice_before_days",
	"invoice_due_after_days",
	"invoice_upcoming_reminder_days",
	"invoice_terms_and_conditions",
	"allow_editing_currency_exchange_rate",
}

// GetSetting returns the value of a setting by its key
func GetSetting(settingKey string) string {
	var setting Setting
	config.DB.Where("setting_key = ?", settingKey).First(&setting)
	return setting.SettingValue
}

// GetSettingByKey returns the complete setting object by its key
func GetSettingByKey(settingKey string) Setting {
	var setting Setting
	config.DB.Where("setting_key = ?", settingKey).First(&setting)
	return setting
}

// GetAllowedSettings returns the list of settings that can be retrieved by frontend users
func GetAllowedSettings() []string {
	return AllowedSettings
}

// IsSettingAllowed checks if a setting key is in the allowed list
func IsSettingAllowed(settingKey string) bool {
	for _, allowedKey := range AllowedSettings {
		if allowedKey == settingKey {
			return true
		}
	}
	return false
}
