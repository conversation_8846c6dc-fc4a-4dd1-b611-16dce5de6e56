package models

import "time"

type Client struct {
	Id              uint       `json:"id" gorm:"primaryKey"`
	CreatedById     uint       `json:"created_by_id"`
	ReferredById    *uint      `json:"referred_by_id"`
	BranchId        *uint      `json:"branch_id"`
	StaffId         *uint      `json:"staff_id"`
	CountryId       *uint      `json:"country_id"`
	CurrencyId      *uint      `json:"currency_id"`
	ClientType      string     `json:"client_type"`
	Gender          *string    `json:"gender"`
	Name            string     `json:"name"`
	Email           string     `json:"email"  gorm:"unique"`
	Company         *string    `json:"company"`
	State           *string    `json:"state"`
	City            *string    `json:"city"`
	Town            *string    `json:"town"`
	Address         *string    `json:"address" gorm:"type:text"`
	PhoneNumber     string     `json:"phone_number"`
	Status          string     `json:"status" gorm:"default:'active'"`
	Description     *string    `json:"description" gorm:"type:text"`
	MapsProvider    *string    `json:"maps_provider" gorm:"default:'google'"`
	BillingCycle    *string    `json:"billing_cycle" gorm:"default:'monthly'"`
	BillingDay      *uint      `json:"billing_day" gorm:"default:5"`
	IsLifetime      *bool      `json:"is_lifetime" gorm:"default:false"`
	NextBillingDate *time.Time `json:"next_billing_date" gorm:"type:date"`
	LastBilledAt    *time.Time `json:"last_billed_at" gorm:"default:null"`
	SuspendedAt     *time.Time `json:"suspended_at" gorm:"default:null"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	Country         Country    `json:"country"`
	Currency        Currency   `json:"currency"`
}
type ClientRequest struct {
	ReferredById    *uint      `json:"referred_by_id,omitempty"`
	StaffId         *uint      `json:"staff_id,omitempty"`
	CountryId       *uint      `json:"country_id,omitempty"`
	CurrencyId      *uint      `json:"currency_id,omitempty"`
	ClientType      string     `json:"client_type" binding:"required"`
	Gender          string     `json:"gender,omitempty"`
	Name            string     `json:"name"  binding:"required"`
	Email           string     `json:"email"   binding:"required,email"`
	Company         string     `json:"company,omitempty"`
	State           string     `json:"state,omitempty"`
	City            string     `json:"city,omitempty"`
	Town            string     `json:"town,omitempty"`
	Address         string     `json:"address,omitempty"`
	PhoneNumber     string     `json:"phone_number" binding:"required"`
	Status          string     `json:"status" binding:"required"`
	Description     string     `json:"description,omitempty"`
	BillingCycle    string     `json:"billing_cycle,omitempty"`
	BillingDay      uint       `json:"billing_day,omitempty"`
	IsLifetime      bool       `json:"is_lifetime,omitempty"`
	NextBillingDate *time.Time `json:"next_billing_date,omitempty"`
	MapsProvider    *string    `json:"maps_provider,omitempty"`
}
type ClientFilterRequest struct {
	StaffId    *uint   `json:"staff_id,omitempty"`
	CountryId  *uint   `json:"country_id,omitempty"`
	ClientType *string `json:"client_type,omitempty"`
}
