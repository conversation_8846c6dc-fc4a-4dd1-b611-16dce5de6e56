package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupFrontendMarkReadTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.<PERSON>env("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()

	// Create basic test data
	createTestData()

	gin.SetMode(gin.TestMode)
}

func createTestData() {
	// Create a test protocol
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.FirstOrCreate(&protocol, models.Protocol{Name: "Test Protocol"})

	// Create a test device type
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.FirstOrCreate(&deviceType, models.DeviceType{Name: "Test Device Type"})
}

func createTestClientAndAlert() (models.Client, models.Alert) {
	// Get the test device type
	var deviceType models.DeviceType
	config.DB.Where("name = ?", "Test Device Type").First(&deviceType)

	// Create a test client with unique email
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
	client := models.Client{
		Name:   "Test Client",
		Email:  email,
		Status: "active",
	}
	result := config.DB.Create(&client)
	if result.Error != nil {
		panic("Failed to create test client: " + result.Error.Error())
	}

	// Create a test client device
	deviceName := "Test Device"
	deviceId := fmt.Sprintf("TEST%d", time.Now().UnixNano())
	clientDevice := models.ClientDevice{
		DeviceTypeId: deviceType.Id,
		ClientId:     client.Id,
		DeviceId:     deviceId,
		Name:         &deviceName,
	}
	result = config.DB.Create(&clientDevice)
	if result.Error != nil {
		panic("Failed to create test client device: " + result.Error.Error())
	}

	// Create a test alert
	alert := models.Alert{
		ClientDeviceId: clientDevice.Id,
		AlertType:      "test_alert",
		AlertTimestamp: time.Now(),
		Read:           nil, // Unread by default
		ReadAt:         nil,
	}
	result = config.DB.Create(&alert)
	if result.Error != nil {
		panic("Failed to create test alert: " + result.Error.Error())
	}

	return client, alert
}

func TestFrontendMarkAlertAsRead(t *testing.T) {
	setupFrontendMarkReadTest()
	client, alert := createTestClientAndAlert()

	defer config.DB.Where("id = ?", alert.Id).Delete(&models.Alert{})
	defer config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
	defer config.DB.Where("id = ?", client.Id).Delete(&models.Client{})

	router := gin.New()
	// Simulate middleware setting client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})
	router.PATCH("/alerts/:id/mark-read", MarkAlertAsRead)

	req, _ := http.NewRequest("PATCH", "/alerts/"+fmt.Sprintf("%d", alert.Id)+"/mark-read", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alert marked as read successfully", response["message"])

	// Verify alert was marked as read
	var updatedAlert models.Alert
	config.DB.First(&updatedAlert, alert.Id)
	assert.NotNil(t, updatedAlert.Read)
	assert.True(t, *updatedAlert.Read)
	assert.NotNil(t, updatedAlert.ReadAt)
}

func TestFrontendMarkAlertAsUnread(t *testing.T) {
	setupFrontendMarkReadTest()
	client, alert := createTestClientAndAlert()

	// First mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now
	config.DB.Save(&alert)

	defer config.DB.Where("id = ?", alert.Id).Delete(&models.Alert{})
	defer config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
	defer config.DB.Where("id = ?", client.Id).Delete(&models.Client{})

	router := gin.New()
	// Simulate middleware setting client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})
	router.PATCH("/alerts/:id/mark-unread", MarkAlertAsUnread)

	req, _ := http.NewRequest("PATCH", "/alerts/"+fmt.Sprintf("%d", alert.Id)+"/mark-unread", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alert marked as unread successfully", response["message"])

	// Verify alert was marked as unread
	var updatedAlert models.Alert
	config.DB.First(&updatedAlert, alert.Id)
	assert.NotNil(t, updatedAlert.Read)
	assert.False(t, *updatedAlert.Read)
	assert.Nil(t, updatedAlert.ReadAt)
}

func TestFrontendBulkMarkAlertsAsRead(t *testing.T) {
	setupFrontendMarkReadTest()
	client, alert1 := createTestClientAndAlert()
	_, alert2 := createTestClientAndAlert()

	defer config.DB.Where("id IN ?", []uint{alert1.Id, alert2.Id}).Delete(&models.Alert{})
	defer config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
	defer config.DB.Where("id = ?", client.Id).Delete(&models.Client{})

	router := gin.New()
	// Simulate middleware setting client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})
	router.PATCH("/alerts/bulk/mark-read", BulkMarkAlertsAsRead)

	reqBody := map[string]interface{}{
		"alert_ids": []uint{alert1.Id, alert2.Id},
	}
	jsonBody, _ := json.Marshal(reqBody)

	req, _ := http.NewRequest("PATCH", "/alerts/bulk/mark-read", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alerts marked as read successfully", response["message"])

	// Verify alerts were marked as read
	var updatedAlert1, updatedAlert2 models.Alert
	config.DB.First(&updatedAlert1, alert1.Id)
	config.DB.First(&updatedAlert2, alert2.Id)

	assert.NotNil(t, updatedAlert1.Read)
	assert.True(t, *updatedAlert1.Read)
	assert.NotNil(t, updatedAlert2.Read)
	assert.True(t, *updatedAlert2.Read)
}

func TestFrontendMarkAllAlertsAsRead(t *testing.T) {
	setupFrontendMarkReadTest()
	client, alert1 := createTestClientAndAlert()
	_, alert2 := createTestClientAndAlert()

	defer config.DB.Where("id IN ?", []uint{alert1.Id, alert2.Id}).Delete(&models.Alert{})
	defer config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
	defer config.DB.Where("id = ?", client.Id).Delete(&models.Client{})

	router := gin.New()
	// Simulate middleware setting client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})
	router.PATCH("/alerts/mark-all-read", MarkAllAlertsAsRead)

	req, _ := http.NewRequest("PATCH", "/alerts/mark-all-read", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "All alerts marked as read successfully", response["message"])

	// Verify alerts were marked as read
	var updatedAlert1, updatedAlert2 models.Alert
	config.DB.First(&updatedAlert1, alert1.Id)
	config.DB.First(&updatedAlert2, alert2.Id)

	assert.NotNil(t, updatedAlert1.Read)
	assert.True(t, *updatedAlert1.Read)
	assert.NotNil(t, updatedAlert2.Read)
	assert.True(t, *updatedAlert2.Read)
}
