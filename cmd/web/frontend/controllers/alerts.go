package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllAlerts(c *gin.Context) {
	var alerts []models.Alert
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}
	if read := c.Query("read"); read != "" {
		filter["read"] = read
	}

	config.DB.Scopes(utils.Paginate(c)).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Order("id desc").Find(&alerts)
	config.DB.Model(&models.Alert{}).Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         alerts,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetAlertById(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")
	if err := config.DB.Preload("ClientDevice").Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice.Client").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": alert,
	})
}

func SearchAlerts(c *gin.Context) {
	var alerts []models.Alert
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}
	if read := c.Query("read"); read != "" {
		filter["read"] = read
	}

	config.DB.Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Order("id desc").Find(&alerts)
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}

// MarkAlertAsRead marks a single alert as read for frontend users
func MarkAlertAsRead(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")

	if err := config.DB.Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId).
		First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as read successfully",
		"data":    alert,
	})
}

// MarkAlertAsUnread marks a single alert as unread for frontend users
func MarkAlertAsUnread(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")

	if err := config.DB.Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId).
		First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as unread
	read := false
	alert.Read = &read
	alert.ReadAt = nil

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as unread successfully",
		"data":    alert,
	})
}

// BulkMarkAlertsAsRead marks multiple alerts as read for frontend users
func BulkMarkAlertsAsRead(c *gin.Context) {
	type BulkMarkRequest struct {
		AlertIds []uint `json:"alert_ids" binding:"required"`
	}

	var req BulkMarkRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	if len(req.AlertIds) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "No alert IDs provided",
		})
		return
	}

	clientId, _ := c.Get("client_id")

	// Update multiple alerts with client restriction
	read := true
	now := time.Now()
	result := config.DB.Model(&models.Alert{}).
		Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId).
		Where("alerts.id IN ?", req.AlertIds).
		Updates(map[string]interface{}{
			"read":    &read,
			"read_at": &now,
		})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}

// MarkAllAlertsAsRead marks all alerts as read for frontend users (with optional filters)
func MarkAllAlertsAsRead(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	query := config.DB.Model(&models.Alert{}).
		Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId)

	// Apply filters if provided
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		if id, err := strconv.Atoi(clientDeviceId); err == nil {
			query = query.Where("alerts.client_device_id = ?", id)
		}
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("alerts.device_id = ?", deviceId)
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		query = query.Where("alerts.alert_type = ?", alertType)
	}

	// Only update unread alerts
	query = query.Where("alerts.read IS NULL OR alerts.read = ?", false)

	read := true
	now := time.Now()
	result := query.Updates(map[string]interface{}{
		"read":    &read,
		"read_at": &now,
	})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "All alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}
