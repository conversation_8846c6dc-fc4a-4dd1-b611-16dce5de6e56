package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllAlerts(c *gin.Context) {
	var alerts []models.Alert
	var total int64
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Order("id desc").Find(&alerts)
	config.DB.Model(&models.Alert{}).Where(filter).Count(&total)
	c.<PERSON>(http.StatusOK, gin.H{
		"data":         alerts,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetAlertById(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.Preload("ClientDevice").Preload("ClientDevice.Client").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": alert,
	})
}

func CreateAlert(c *gin.Context) {

	var req models.CreateAlertRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	alert := models.Alert{
		ClientDeviceId: req.ClientDeviceId,
		DeviceId:       req.DeviceId,
		AlertType:      req.AlertType,
		AlertName:      req.AlertName,
		Message:        req.Message,
		RawData:        req.RawData,
		AdditionalData: req.AdditionalData,
		Speed:          req.Speed,
		Direction:      req.Direction,
		AlertTimestamp: req.AlertTimestamp,
	}
	result := config.DB.Create(&alert)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert created successfully",
	})
}

func UpdateAlert(c *gin.Context) {
	var req models.UpdateAlertRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var alert models.Alert
	if err := config.DB.First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Update read status and timestamp
	if req.Read != nil {
		alert.Read = req.Read
		if *req.Read {
			now := time.Now()
			alert.ReadAt = &now
		} else {
			alert.ReadAt = nil
		}
	}

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "Alert updated successfully",
		"data":    alert,
	})
}

func DeleteAlert(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	result := config.DB.Delete(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert deleted successfully",
	})
}

func SearchAlerts(c *gin.Context) {
	var alerts []models.Alert
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	config.DB.Where(filter).Preload("ClientDevice").Preload("ClientDevice.Client").Order("id desc").Find(&alerts)
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}

// MarkAlertAsRead marks a single alert as read
func MarkAlertAsRead(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as read successfully",
		"data":    alert,
	})
}

// MarkAlertAsUnread marks a single alert as unread
func MarkAlertAsUnread(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as unread
	read := false
	alert.Read = &read
	alert.ReadAt = nil

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as unread successfully",
		"data":    alert,
	})
}

// BulkMarkAlertsAsRead marks multiple alerts as read
func BulkMarkAlertsAsRead(c *gin.Context) {
	type BulkMarkRequest struct {
		AlertIds []uint `json:"alert_ids" binding:"required"`
	}

	var req BulkMarkRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	if len(req.AlertIds) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "No alert IDs provided",
		})
		return
	}

	// Update multiple alerts
	read := true
	now := time.Now()
	result := config.DB.Model(&models.Alert{}).
		Where("id IN ?", req.AlertIds).
		Updates(map[string]interface{}{
			"read":    &read,
			"read_at": &now,
		})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}

// MarkAllAlertsAsRead marks all alerts as read (with optional filters)
func MarkAllAlertsAsRead(c *gin.Context) {
	query := config.DB.Model(&models.Alert{})

	// Apply filters if provided
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		if id, err := strconv.Atoi(clientDeviceId); err == nil {
			query = query.Where("client_device_id = ?", id)
		}
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("device_id = ?", deviceId)
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		query = query.Where("alert_type = ?", alertType)
	}

	// Only update unread alerts
	query = query.Where("read IS NULL OR read = ?", false)

	read := true
	now := time.Now()
	result := query.Updates(map[string]interface{}{
		"read":    &read,
		"read_at": &now,
	})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "All alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}
